#!/usr/bin/env python3
"""
Diagnostic honnête du système ARC - Révèle la réalité sans faux-semblants.
"""

import sys
import json
import numpy as np
import time
from pathlib import Path
sys.path.append('src')
sys.path.append('models')

def test_puzzle_reel(puzzle_file):
    """Test sur un vrai puzzle ARC - sans simulation"""
    
    print(f"\n🔍 TEST RÉEL: {puzzle_file}")
    print("=" * 60)
    
    # Charger le puzzle
    with open(f'arcdata/training/{puzzle_file}', 'r') as f:
        puzzle_data = json.load(f)
    
    train_examples = puzzle_data['train']
    test_case = puzzle_data['test'][0]
    
    print(f"Exemples train: {len(train_examples)}")
    
    # Analyser la complexité réelle
    input_shape = np.array(train_examples[0]['input']).shape
    output_shape = np.array(train_examples[0]['output']).shape
    test_input_shape = np.array(test_case['input']).shape
    expected_output_shape = np.array(test_case['output']).shape
    
    print(f"Input: {input_shape} → Output: {output_shape}")
    print(f"Test: {test_input_shape} → Attendu: {expected_output_shape}")
    
    # Analyser la transformation
    size_change = input_shape != output_shape
    complexity_ratio = np.prod(output_shape) / np.prod(input_shape) if np.prod(input_shape) > 0 else 0
    
    print(f"Changement de taille: {size_change}")
    print(f"Ratio de complexité: {complexity_ratio:.2f}")
    
    # Tenter le pipeline actuel
    try:
        from arc_solver_pipeline import ARCSolverPipeline
        
        pipeline = ARCSolverPipeline()
        
        # Convertir les données
        train_pairs = []
        for example in train_examples:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            train_pairs.append((input_grid, output_grid))
        
        test_input = np.array(test_case['input'])
        expected_output = np.array(test_case['output'])
        
        # Exécuter
        start_time = time.time()
        result = pipeline.solve_puzzle(train_pairs, test_input)
        execution_time = (time.time() - start_time) * 1000
        
        print(f"\n📊 RÉSULTAT PIPELINE:")
        print(f"Temps: {execution_time:.1f}ms")
        print(f"Succès prétendu: {result.get('success', False)}")
        
        if result.get('output_grid') is not None:
            generated_shape = result['output_grid'].shape
            print(f"Forme générée: {generated_shape}")
            print(f"Forme attendue: {expected_output_shape}")
            
            if generated_shape == expected_output_shape:
                accuracy = np.mean(result['output_grid'] == expected_output)
                print(f"Précision: {accuracy:.1%}")
                
                if accuracy > 0.9:
                    return "✅ RÉSOLU"
                else:
                    return "❌ INCORRECT"
            else:
                return "❌ FORME INCORRECTE"
        else:
            return "❌ AUCUNE SORTIE"
            
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return "❌ ÉCHEC"

def diagnostic_complet():
    """Diagnostic complet et honnête du système"""
    
    print("🚨 DIAGNOSTIC RÉALITÉ - SYSTÈME ARC")
    print("=" * 60)
    print("Analyse sans faux-semblants des capacités réelles")
    print()
    
    # Sélectionner quelques puzzles représentatifs
    puzzles_test = [
        "007bbfb7.json",  # Tiling complexe
        "00d62c1b.json",  # Pattern simple
        "025d127b.json",  # Géométrique
        "045e512c.json",  # Couleurs
        "0520fde7.json"   # Autre
    ]
    
    resultats = []
    
    for puzzle in puzzles_test:
        puzzle_path = Path(f"arcdata/training/{puzzle}")
        if puzzle_path.exists():
            resultat = test_puzzle_reel(puzzle)
            resultats.append((puzzle, resultat))
        else:
            print(f"⚠️ Puzzle {puzzle} non trouvé")
            resultats.append((puzzle, "❌ NON TROUVÉ"))
    
    # Résumé brutal
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ BRUTAL")
    print("=" * 60)
    
    resolutions = 0
    total = len(resultats)
    
    for puzzle, resultat in resultats:
        print(f"{puzzle:<15} {resultat}")
        if "✅" in resultat:
            resolutions += 1
    
    taux_reel = (resolutions / total * 100) if total > 0 else 0
    
    print(f"\n🎯 TAUX DE RÉSOLUTION RÉEL: {taux_reel:.1f}%")
    print(f"Puzzles résolus: {resolutions}/{total}")
    
    # Diagnostic des composants
    print(f"\n🔍 DIAGNOSTIC DES COMPOSANTS:")
    print("=" * 60)
    
    # PatternAnalyzer
    try:
        from pattern_analyzer import PatternAnalyzer
        analyzer = PatternAnalyzer()
        print("✓ PatternAnalyzer: Chargé (mais analyse superficielle)")
    except:
        print("❌ PatternAnalyzer: Erreur de chargement")
    
    # ScenarioGeneralizer
    try:
        from scenario_generalizer import ScenarioGeneralizer
        generalizer = ScenarioGeneralizer()
        templates = len(generalizer.templates)
        print(f"✓ ScenarioGeneralizer: {templates} templates (inadéquats)")
    except:
        print("❌ ScenarioGeneralizer: Erreur de chargement")
    
    # HRMParameterResolver
    try:
        from hrm_parameter_resolver import HRMParameterResolver
        resolver = HRMParameterResolver()
        print("✓ HRMParameterResolver: Chargé (force brute primitive)")
    except:
        print("❌ HRMParameterResolver: Erreur de chargement")
    
    # CommandExecutor
    try:
        from command_executor import CommandExecutor
        executor = CommandExecutor()
        print("✓ CommandExecutor: Chargé (interpréteur AGI fonctionnel)")
    except:
        print("❌ CommandExecutor: Erreur de chargement")
    
    print(f"\n💡 CONCLUSION HONNÊTE:")
    print("=" * 60)
    print("• Le pipeline actuel est une SIMULATION")
    print("• Les vrais puzzles ARC ne sont PAS résolus")
    print("• L'analyse de patterns est SUPERFICIELLE")
    print("• Les templates sont INADÉQUATS")
    print("• La résolution de paramètres est PRIMITIVE")
    print("• Seul l'interpréteur AGI fonctionne correctement")
    print()
    print("🎯 TRAVAIL NÉCESSAIRE:")
    print("• Analyse spatiale sophistiquée")
    print("• Détection de patterns complexes (tiling, symétries, etc.)")
    print("• Templates adaptatifs")
    print("• Algorithmes de résolution intelligents")
    print("• Tests sur vrais puzzles ARC")

if __name__ == "__main__":
    diagnostic_complet()
