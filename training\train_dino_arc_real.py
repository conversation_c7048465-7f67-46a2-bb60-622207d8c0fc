#!/usr/bin/env python3
"""
Entraînement du modèle DINO-ARC sur vraies données ARC.
Approche honnête : entraînement contrastif + catégorisation supervisée.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import numpy as np
from pathlib import Path
from tqdm import tqdm
import logging
from dataclasses import dataclass
from typing import List, Tuple, Dict, Any

import sys
sys.path.append('..')
from models.dino_arc import DinoARC, ContrastiveLoss, apply_augmentation
from src.multi_example_dataset import MultiExampleARCDataset

# Configuration d'entraînement
@dataclass
class DinoTrainingConfig:
    # Modèle
    grid_size: int = 30
    vocab_size: int = 10
    embed_dim: int = 128
    num_layers: int = 4
    num_heads: int = 8
    num_categories: int = 15  # Catégories ARC réalistes
    
    # Entraînement
    batch_size: int = 8
    learning_rate: float = 1e-4
    num_epochs: int = 50
    warmup_epochs: int = 5
    
    # Contrastif
    contrastive_weight: float = 0.3
    temperature: float = 0.07
    
    # Données
    max_puzzles: int = 200  # Limiter pour test
    train_split: float = 0.8
    
    # Sauvegarde
    save_every: int = 10
    checkpoint_dir: str = "dino_checkpoints"
    log_every: int = 20
    
    # Device
    device: str = "cuda" if torch.cuda.is_available() else "cpu"

class ARCPuzzleDataset(Dataset):
    """Dataset pour l'entraînement DINO-ARC."""
    
    def __init__(self, data_dir="../arcdata/training", max_puzzles=None):
        self.data_dir = Path(data_dir)
        self.puzzle_files = list(self.data_dir.glob("*.json"))
        
        if max_puzzles:
            self.puzzle_files = self.puzzle_files[:max_puzzles]
        
        # Catégories manuelles basées sur l'analyse des puzzles
        self.categories = [
            "color_fill", "geometric_transform", "pattern_repetition",
            "size_change", "object_counting", "symmetry_detection",
            "grid_completion", "shape_recognition", "color_mapping",
            "spatial_reasoning", "pattern_overlay", "grid_transformation",
            "object_manipulation", "rule_application", "complex_pattern"
        ]
        
        # Mapping puzzle -> catégorie (à améliorer avec analyse automatique)
        self.puzzle_categories = self._assign_categories()
    
    def _assign_categories(self):
        """Assigne des catégories aux puzzles (version simplifiée)."""
        categories = {}
        
        for i, puzzle_file in enumerate(self.puzzle_files):
            # Pour l'instant, assignation cyclique
            # TODO: Améliorer avec analyse automatique des patterns
            category_idx = i % len(self.categories)
            categories[puzzle_file.stem] = category_idx
        
        return categories
    
    def __len__(self):
        return len(self.puzzle_files)
    
    def __getitem__(self, idx):
        puzzle_file = self.puzzle_files[idx]
        
        with open(puzzle_file, 'r') as f:
            puzzle_data = json.load(f)

        # Vérifier que le puzzle a des données train
        if 'train' not in puzzle_data or not puzzle_data['train']:
            # Retourner un exemple vide pour éviter l'erreur
            return {
                'train_pairs': [],
                'category': 0,
                'puzzle_id': puzzle_file.stem
            }

        # Préparer les paires train
        train_pairs = []
        for example in puzzle_data['train']:
            input_grid = torch.tensor(example['input'], dtype=torch.long)
            output_grid = torch.tensor(example['output'], dtype=torch.long)
            
            # Padding à grid_size
            input_padded = torch.zeros(30, 30, dtype=torch.long)
            output_padded = torch.zeros(30, 30, dtype=torch.long)
            
            h_in, w_in = input_grid.shape
            h_out, w_out = output_grid.shape
            
            input_padded[:h_in, :w_in] = input_grid
            output_padded[:h_out, :w_out] = output_grid
            
            train_pairs.append((input_padded, output_padded))
        
        # Catégorie du puzzle
        puzzle_id = puzzle_file.stem
        category = self.puzzle_categories.get(puzzle_id, 0)
        
        return {
            'train_pairs': train_pairs,
            'category': category,
            'puzzle_id': puzzle_id
        }

def collate_fn(batch):
    """Fonction de collation pour le DataLoader."""
    # Prendre le premier élément pour simplifier
    # TODO: Améliorer pour traiter des batches complets
    return batch[0]

class DinoTrainer:
    """Entraîneur pour DINO-ARC."""
    
    def __init__(self, config: DinoTrainingConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # Modèle
        self.model = DinoARC(
            grid_size=config.grid_size,
            vocab_size=config.vocab_size,
            embed_dim=config.embed_dim,
            num_layers=config.num_layers,
            num_heads=config.num_heads,
            num_categories=config.num_categories
        ).to(self.device)
        
        # Pertes
        self.contrastive_loss = ContrastiveLoss(config.temperature)
        self.category_loss = nn.CrossEntropyLoss()
        
        # Optimiseur
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.learning_rate,
            weight_decay=1e-4
        )
        
        # Scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=config.num_epochs
        )
        
        # Logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Métriques
        self.train_losses = []
        self.category_accuracies = []
    
    def train_epoch(self, dataloader):
        """Entraîne une époque."""
        self.model.train()
        total_loss = 0
        correct_predictions = 0
        total_samples = 0
        
        pbar = tqdm(dataloader, desc="Training")
        
        for batch_idx, batch in enumerate(pbar):
            # Ignorer les puzzles vides
            if not batch['train_pairs']:
                continue

            self.optimizer.zero_grad()

            train_pairs = batch['train_pairs']
            true_category = torch.tensor([batch['category']], device=self.device)
            
            # Préparer les paires avec dimension batch
            batch_pairs = []
            for input_grid, output_grid in train_pairs:
                input_batch = input_grid.unsqueeze(0).to(self.device)
                output_batch = output_grid.unsqueeze(0).to(self.device)
                batch_pairs.append((input_batch, output_batch))
            
            # Forward pass
            result = self.model(batch_pairs)
            
            # Perte de catégorisation
            category_logits = result['category_logits']
            cat_loss = self.category_loss(category_logits, true_category)
            
            # Perte contrastive (entre input et output features)
            if len(batch_pairs) > 1:
                # Utiliser les features des différents exemples
                input_features = result['pattern_features']
                # Créer une version augmentée pour le contrastif
                aug_pairs = []
                for input_grid, output_grid in train_pairs[:2]:  # Limiter à 2 pour mémoire
                    aug_input = apply_augmentation(input_grid.unsqueeze(0), 'rotation')
                    aug_output = apply_augmentation(output_grid.unsqueeze(0), 'rotation')
                    aug_pairs.append((aug_input.to(self.device), aug_output.to(self.device)))
                
                aug_result = self.model(aug_pairs)
                aug_features = aug_result['pattern_features']
                
                contrastive_loss = self.contrastive_loss(input_features, aug_features)
            else:
                contrastive_loss = torch.tensor(0.0, device=self.device)
            
            # Perte totale
            total_loss_batch = cat_loss + self.config.contrastive_weight * contrastive_loss
            
            # Backward pass
            total_loss_batch.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            
            # Métriques
            total_loss += total_loss_batch.item()
            pred_category = torch.argmax(category_logits, dim=1)
            correct_predictions += (pred_category == true_category).sum().item()
            total_samples += 1
            
            # Logging
            if batch_idx % self.config.log_every == 0:
                pbar.set_postfix({
                    'loss': f'{total_loss_batch.item():.4f}',
                    'cat_loss': f'{cat_loss.item():.4f}',
                    'cont_loss': f'{contrastive_loss.item():.4f}',
                    'acc': f'{correct_predictions/total_samples:.3f}'
                })
        
        avg_loss = total_loss / len(dataloader)
        accuracy = correct_predictions / total_samples
        
        return avg_loss, accuracy
    
    def train(self, train_dataset):
        """Entraînement complet."""
        # DataLoader
        train_loader = DataLoader(
            train_dataset,
            batch_size=1,  # Un puzzle à la fois pour simplifier
            shuffle=True,
            collate_fn=collate_fn
        )
        
        self.logger.info(f"Début entraînement DINO-ARC")
        self.logger.info(f"Dataset: {len(train_dataset)} puzzles")
        self.logger.info(f"Modèle: {sum(p.numel() for p in self.model.parameters()):,} paramètres")
        
        best_accuracy = 0
        
        for epoch in range(self.config.num_epochs):
            # Entraînement
            avg_loss, accuracy = self.train_epoch(train_loader)
            
            # Scheduler
            self.scheduler.step()
            
            # Métriques
            self.train_losses.append(avg_loss)
            self.category_accuracies.append(accuracy)
            
            # Logging
            self.logger.info(
                f"Epoch {epoch+1}/{self.config.num_epochs} - "
                f"Loss: {avg_loss:.4f}, Accuracy: {accuracy:.3f}, "
                f"LR: {self.scheduler.get_last_lr()[0]:.6f}"
            )
            
            # Sauvegarde
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                self.save_model(f"dino_arc_best.pth")
            
            if (epoch + 1) % self.config.save_every == 0:
                self.save_model(f"dino_arc_epoch_{epoch+1}.pth")
        
        self.logger.info(f"Entraînement terminé. Meilleure accuracy: {best_accuracy:.3f}")
        return best_accuracy
    
    def save_model(self, filename):
        """Sauvegarde le modèle."""
        checkpoint_dir = Path(self.config.checkpoint_dir)
        checkpoint_dir.mkdir(exist_ok=True)
        
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config,
            'train_losses': self.train_losses,
            'category_accuracies': self.category_accuracies
        }, checkpoint_dir / filename)

def main():
    """Fonction principale d'entraînement."""
    print("🚀 ENTRAÎNEMENT DINO-ARC")
    print("=" * 50)
    
    # Configuration
    config = DinoTrainingConfig()
    print(f"Device: {config.device}")
    print(f"Max puzzles: {config.max_puzzles}")
    
    # Dataset
    dataset = ARCPuzzleDataset(max_puzzles=config.max_puzzles)
    print(f"Dataset chargé: {len(dataset)} puzzles")
    print(f"Catégories: {len(dataset.categories)}")
    
    # Entraîneur
    trainer = DinoTrainer(config)
    
    # Entraînement
    best_accuracy = trainer.train(dataset)
    
    print(f"\n✅ Entraînement terminé")
    print(f"Meilleure accuracy: {best_accuracy:.3f}")
    print(f"Modèle sauvé dans: {config.checkpoint_dir}")

if __name__ == "__main__":
    main()
