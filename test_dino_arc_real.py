#!/usr/bin/env python3
"""
Test du modèle DINO-ARC sur de vraies données ARC.
Honnêteté technique : test réel sans simulation.
"""

import torch
import json
import numpy as np
from pathlib import Path
from models.dino_arc import DinoARC
from src.multi_example_dataset import MultiExampleARCDataset

def load_real_puzzle(puzzle_id="007bbfb7"):
    """Charge un puzzle ARC réel pour test."""
    puzzle_path = Path(f"arcdata/training/{puzzle_id}.json")
    
    if not puzzle_path.exists():
        raise FileNotFoundError(f"Puzzle {puzzle_id} non trouvé")
    
    with open(puzzle_path, 'r') as f:
        puzzle_data = json.load(f)
    
    return puzzle_data

def prepare_train_pairs(puzzle_data):
    """Prépare les paires train pour DINO-ARC."""
    train_pairs = []
    
    for example in puzzle_data['train']:
        input_grid = torch.tensor(example['input'], dtype=torch.long)
        output_grid = torch.tensor(example['output'], dtype=torch.long)
        
        # Padding à 30x30 si nécessaire
        input_padded = torch.zeros(30, 30, dtype=torch.long)
        output_padded = torch.zeros(30, 30, dtype=torch.long)
        
        h_in, w_in = input_grid.shape
        h_out, w_out = output_grid.shape
        
        input_padded[:h_in, :w_in] = input_grid
        output_padded[:h_out, :w_out] = output_grid
        
        # Ajouter dimension batch
        input_batch = input_padded.unsqueeze(0)
        output_batch = output_padded.unsqueeze(0)
        
        train_pairs.append((input_batch, output_batch))
    
    return train_pairs

def test_dino_arc_analysis():
    """Test d'analyse DINO-ARC sur un puzzle réel."""
    print("🧪 TEST DINO-ARC SUR DONNÉES RÉELLES")
    print("=" * 50)
    
    try:
        # Charger un puzzle réel
        puzzle_data = load_real_puzzle("007bbfb7")
        print(f"✓ Puzzle chargé: {len(puzzle_data['train'])} exemples train")
        
        # Préparer les données
        train_pairs = prepare_train_pairs(puzzle_data)
        print(f"✓ Paires préparées: {len(train_pairs)}")
        
        # Créer le modèle DINO-ARC
        model = DinoARC(
            grid_size=30,
            vocab_size=10,
            embed_dim=128,
            num_layers=4,  # Plus petit pour test
            num_heads=8,
            num_categories=20
        )
        
        print(f"✓ Modèle DINO-ARC créé")
        print(f"  - Paramètres: {sum(p.numel() for p in model.parameters()):,}")
        
        # Test d'analyse
        model.eval()
        with torch.no_grad():
            result = model(train_pairs)
        
        print("\n📊 RÉSULTATS D'ANALYSE")
        print("-" * 30)
        print(f"Catégorie prédite: {result['category']}")
        print(f"Confiance: {result['confidence']:.3f}")
        print(f"Features shape: {result['pattern_features'].shape}")
        print(f"Pattern maps: {list(result['pattern_maps'].keys())}")
        
        # Analyser les patterns détectés
        print("\n🔍 PATTERNS DÉTECTÉS")
        print("-" * 30)
        for ptype, pmap in result['pattern_maps'].items():
            activation = pmap.mean().item()
            print(f"{ptype}: {activation:.3f}")
        
        print("\n✅ Test DINO-ARC réussi")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur dans test DINO-ARC: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_puzzle_analysis():
    """Test sur plusieurs puzzles pour voir la variabilité."""
    print("\n🔄 TEST MULTI-PUZZLES")
    print("=" * 50)
    
    puzzle_ids = ["007bbfb7", "00d62c1b", "017c7c7b", "025d127b"]
    results = {}
    
    model = DinoARC(grid_size=30, vocab_size=10, embed_dim=64, num_layers=2)
    model.eval()
    
    for puzzle_id in puzzle_ids:
        try:
            puzzle_data = load_real_puzzle(puzzle_id)
            train_pairs = prepare_train_pairs(puzzle_data)
            
            with torch.no_grad():
                result = model(train_pairs)
            
            results[puzzle_id] = {
                'category': result['category'],
                'confidence': result['confidence'],
                'num_examples': len(train_pairs)
            }
            
            print(f"{puzzle_id}: {result['category']} (conf: {result['confidence']:.3f})")
            
        except Exception as e:
            print(f"{puzzle_id}: ERREUR - {e}")
            results[puzzle_id] = {'error': str(e)}
    
    print(f"\n✓ Analysé {len(results)} puzzles")
    return results

if __name__ == "__main__":
    print("🎯 TEST DINO-ARC - HONNÊTETÉ TECHNIQUE")
    print("Analyse réelle de puzzles ARC sans simulation")
    print()
    
    # Test de base
    success = test_dino_arc_analysis()
    
    if success:
        # Test multi-puzzles
        multi_results = test_multi_puzzle_analysis()
        
        print("\n📈 RÉSUMÉ")
        print("=" * 50)
        print("✅ DINO-ARC fonctionne sur vraies données")
        print("✅ Catégorisation automatique opérationnelle")
        print("✅ Détection de patterns basique")
        print()
        print("⚠️  LIMITATIONS IDENTIFIÉES:")
        print("- Catégories prédites peuvent être arbitraires")
        print("- Pas d'entraînement supervisé encore")
        print("- Patterns détectés sont basiques")
        print()
        print("🎯 PROCHAINE ÉTAPE: Entraînement supervisé")
    else:
        print("\n❌ ÉCHEC DU TEST")
        print("Le modèle DINO-ARC nécessite des corrections")
