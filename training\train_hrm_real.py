#!/usr/bin/env python3
"""
Entraînement du modèle HRM sur vraies données ARC.
Approche honnête : génération de programmes réels pour résolution de puzzles.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import numpy as np
from pathlib import Path
from tqdm import tqdm
import logging
from dataclasses import dataclass
from typing import List, Tuple, Dict, Any, Optional

import sys
sys.path.append('..')
from models.hrm_model import GridToProgramHRM
from models.hrm_parameter_resolver import HRMParameterResolver
from src.command_executor import CommandExecutor

# Configuration d'entraînement
@dataclass
class HRMTrainingConfig:
    # Modèle
    model_dim: int = 128
    n_heads: int = 8
    grammar_vocab_size: int = 200
    max_grid_size: int = 900
    N_cycles: int = 3
    T_steps: int = 4
    
    # Entraînement
    batch_size: int = 4  # Petit batch pour mémoire
    learning_rate: float = 1e-4
    num_epochs: int = 30
    warmup_epochs: int = 3
    
    # Données
    max_puzzles: int = 100  # Limiter pour test
    train_split: float = 0.8
    max_program_length: int = 50
    
    # Sauvegarde
    save_every: int = 5
    checkpoint_dir: str = "hrm_checkpoints"
    log_every: int = 10
    
    # Device
    device: str = "cuda" if torch.cuda.is_available() else "cpu"

# Vocabulaire de grammaire simplifié
GRAMMAR_VOCAB = {
    '<pad>': 0, '<sos>': 1, '<eos>': 2, '<unk>': 3,
    'INIT': 4, 'FILL': 5, 'REPLACE': 6, 'COPY': 7, 'MOVE': 8,
    'ROTATE': 9, 'FLIP': 10, 'SCALE': 11, 'CROP': 12,
    '[': 13, ']': 14, ',': 15, 'x': 16,
    '0': 17, '1': 18, '2': 19, '3': 20, '4': 21, '5': 22,
    '6': 23, '7': 24, '8': 25, '9': 26, '10': 27
}

# Ajouter plus de tokens pour atteindre 200
for i in range(28, 200):
    GRAMMAR_VOCAB[f'token_{i}'] = i

VOCAB_SIZE = len(GRAMMAR_VOCAB)
REVERSE_VOCAB = {v: k for k, v in GRAMMAR_VOCAB.items()}

class ARCHRMDataset(Dataset):
    """Dataset pour l'entraînement HRM sur puzzles ARC."""
    
    def __init__(self, data_dir="../arcdata/training", max_puzzles=None, config=None):
        self.data_dir = Path(data_dir)
        self.config = config or HRMTrainingConfig()
        
        # Charger les puzzles valides
        all_json_files = list(self.data_dir.glob("*.json"))
        self.puzzle_files = []
        
        for file in all_json_files:
            if len(file.stem) == 8 and all(c in '0123456789abcdef' for c in file.stem.lower()):
                self.puzzle_files.append(file)
        
        if max_puzzles:
            self.puzzle_files = self.puzzle_files[:max_puzzles]
        
        # Résolveur de paramètres pour générer les programmes
        self.parameter_resolver = HRMParameterResolver()
        
        # Templates de scénarios simples
        self.scenario_templates = [
            "FILL {color} [{x1},{y1} {x2},{y2}]",
            "REPLACE {old_color} {new_color} [{x1},{y1} {x2},{y2}]",
            "COPY [{x1},{y1} {x2},{y2}] [{x3},{y3} {x4},{y4}]"
        ]
        
        # Cache des programmes générés
        self.program_cache = {}
        
        print(f"Dataset HRM créé: {len(self.puzzle_files)} puzzles")
    
    def __len__(self):
        return len(self.puzzle_files)
    
    def tokenize_program(self, program: str) -> List[int]:
        """Tokenise un programme en utilisant le vocabulaire."""
        tokens = [GRAMMAR_VOCAB['<sos>']]
        
        # Tokenisation simple par mots
        words = program.split()
        for word in words:
            if word in GRAMMAR_VOCAB:
                tokens.append(GRAMMAR_VOCAB[word])
            else:
                # Essayer de décomposer les mots complexes
                found = False
                for vocab_word in GRAMMAR_VOCAB:
                    if vocab_word in word and len(vocab_word) > 1:
                        tokens.append(GRAMMAR_VOCAB[vocab_word])
                        found = True
                        break
                if not found:
                    tokens.append(GRAMMAR_VOCAB['<unk>'])
        
        tokens.append(GRAMMAR_VOCAB['<eos>'])
        
        # Padding/truncation
        if len(tokens) > self.config.max_program_length:
            tokens = tokens[:self.config.max_program_length]
        else:
            tokens.extend([GRAMMAR_VOCAB['<pad>']] * (self.config.max_program_length - len(tokens)))
        
        return tokens
    
    def generate_program_for_puzzle(self, puzzle_data: Dict) -> Optional[str]:
        """Génère un programme pour résoudre un puzzle."""
        try:
            train_examples = []
            for example in puzzle_data['train']:
                input_grid = np.array(example['input'])
                output_grid = np.array(example['output'])
                train_examples.append((input_grid, output_grid))
            
            if not train_examples:
                return None
            
            # Essayer différents templates
            for template in self.scenario_templates:
                try:
                    test_input = train_examples[0][0]  # Utiliser le premier exemple comme test
                    resolved = self.parameter_resolver.resolve_parameters(
                        template, test_input, train_examples
                    )
                    
                    if resolved and resolved != template:
                        return resolved
                except:
                    continue
            
            # Fallback : programme simple basé sur les dimensions
            input_shape = train_examples[0][0].shape
            return f"FILL 1 [0,0 {input_shape[1]-1},{input_shape[0]-1}]"
            
        except Exception as e:
            return None
    
    def __getitem__(self, idx):
        puzzle_file = self.puzzle_files[idx]
        puzzle_id = puzzle_file.stem
        
        # Utiliser le cache si disponible
        if puzzle_id in self.program_cache:
            grid, program_tokens = self.program_cache[puzzle_id]
        else:
            # Charger le puzzle
            with open(puzzle_file, 'r') as f:
                puzzle_data = json.load(f)
            
            # Prendre le premier exemple d'entraînement
            if not puzzle_data.get('train'):
                # Puzzle vide, retourner des données par défaut
                grid = torch.zeros(3, 3, dtype=torch.long)
                program_tokens = torch.tensor(self.tokenize_program("FILL 0 [0,0 2,2]"), dtype=torch.long)
            else:
                input_grid = np.array(puzzle_data['train'][0]['input'])
                
                # Padding à une taille fixe (max 30x30)
                max_size = 30
                padded_grid = np.zeros((max_size, max_size), dtype=int)
                h, w = input_grid.shape
                padded_grid[:h, :w] = input_grid
                
                grid = torch.tensor(padded_grid, dtype=torch.long)
                
                # Générer le programme
                program = self.generate_program_for_puzzle(puzzle_data)
                if program is None:
                    program = "FILL 1 [0,0 2,2]"  # Programme par défaut
                
                program_tokens = torch.tensor(self.tokenize_program(program), dtype=torch.long)
            
            # Mettre en cache
            self.program_cache[puzzle_id] = (grid, program_tokens)
        
        return {
            'grid': grid,
            'program_tokens': program_tokens,
            'puzzle_id': puzzle_id
        }

def collate_fn(batch):
    """Fonction de collation pour le DataLoader."""
    grids = torch.stack([item['grid'] for item in batch])
    program_tokens = torch.stack([item['program_tokens'] for item in batch])
    puzzle_ids = [item['puzzle_id'] for item in batch]
    
    return {
        'grids': grids,
        'program_tokens': program_tokens,
        'puzzle_ids': puzzle_ids
    }

class HRMTrainer:
    """Entraîneur pour le modèle HRM."""
    
    def __init__(self, config: HRMTrainingConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # Modèle
        self.model = GridToProgramHRM(
            model_dim=config.model_dim,
            n_heads=config.n_heads,
            grammar_vocab_size=config.grammar_vocab_size,
            max_grid_size=config.max_grid_size,
            N_cycles=config.N_cycles,
            T_steps=config.T_steps
        ).to(self.device)
        
        # Perte et optimiseur
        self.criterion = nn.CrossEntropyLoss(ignore_index=GRAMMAR_VOCAB['<pad>'])
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.learning_rate,
            weight_decay=1e-4
        )
        
        # Scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=config.num_epochs
        )
        
        # Logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Métriques
        self.train_losses = []
        self.train_accuracies = []
    
    def train_epoch(self, dataloader):
        """Entraîne une époque."""
        self.model.train()
        total_loss = 0
        total_correct = 0
        total_tokens = 0
        
        pbar = tqdm(dataloader, desc="Training")
        
        for batch_idx, batch in enumerate(pbar):
            self.optimizer.zero_grad()
            
            grids = batch['grids'].to(self.device)
            program_tokens = batch['program_tokens'].to(self.device)
            
            # Forward pass
            segments, q_values = self.model(grids, program_tokens, max_segments=3)

            # Vérifier qu'on a des segments
            if not segments:
                # Pas de segments générés, passer ce batch
                continue

            # Calculer la perte sur tous les segments
            total_loss_batch = 0
            total_correct_batch = 0
            total_tokens_batch = 0

            for segment_logits in segments:
                # Teacher forcing: décaler les tokens
                input_tokens = program_tokens[:, :-1]
                target_tokens = program_tokens[:, 1:]

                # Debug des dimensions
                print(f"segment_logits shape: {segment_logits.shape}")
                print(f"target_tokens shape: {target_tokens.shape}")

                # Assurer que les dimensions correspondent
                seq_len = min(segment_logits.size(1), target_tokens.size(1))
                segment_logits_trimmed = segment_logits[:, :seq_len, :]
                target_tokens_trimmed = target_tokens[:, :seq_len]

                # Reshape pour la perte
                logits_flat = segment_logits_trimmed.reshape(-1, segment_logits_trimmed.size(-1))
                targets_flat = target_tokens_trimmed.reshape(-1)
                
                # Calculer la perte
                loss = self.criterion(logits_flat, targets_flat)
                total_loss_batch += loss
                
                # Calculer l'accuracy
                predictions = torch.argmax(logits_flat, dim=-1)
                mask = targets_flat != GRAMMAR_VOCAB['<pad>']
                correct = (predictions == targets_flat) & mask
                total_correct_batch += correct.sum().item()
                total_tokens_batch += mask.sum().item()
            
            # Moyenne sur les segments
            if segments:
                total_loss_batch /= len(segments)

                # Backward pass seulement si on a une perte
                total_loss_batch.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                self.optimizer.step()
            else:
                # Pas de segments, perte nulle
                total_loss_batch = torch.tensor(0.0, device=self.device)
            
            # Métriques
            total_loss += total_loss_batch.item()
            total_correct += total_correct_batch
            total_tokens += total_tokens_batch
            
            # Logging
            if batch_idx % self.config.log_every == 0:
                current_acc = total_correct_batch / max(total_tokens_batch, 1)
                pbar.set_postfix({
                    'loss': f'{total_loss_batch.item():.4f}',
                    'acc': f'{current_acc:.3f}',
                    'segments': len(segments)
                })
        
        avg_loss = total_loss / len(dataloader)
        avg_accuracy = total_correct / max(total_tokens, 1)
        
        return avg_loss, avg_accuracy
    
    def train(self, train_dataset):
        """Entraînement complet."""
        # DataLoader
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.batch_size,
            shuffle=True,
            collate_fn=collate_fn
        )
        
        self.logger.info(f"Début entraînement HRM")
        self.logger.info(f"Dataset: {len(train_dataset)} puzzles")
        self.logger.info(f"Modèle: {sum(p.numel() for p in self.model.parameters()):,} paramètres")
        self.logger.info(f"Vocabulaire: {VOCAB_SIZE} tokens")
        
        best_accuracy = 0
        
        for epoch in range(self.config.num_epochs):
            # Entraînement
            avg_loss, avg_accuracy = self.train_epoch(train_loader)
            
            # Scheduler
            self.scheduler.step()
            
            # Métriques
            self.train_losses.append(avg_loss)
            self.train_accuracies.append(avg_accuracy)
            
            # Logging
            self.logger.info(
                f"Epoch {epoch+1}/{self.config.num_epochs} - "
                f"Loss: {avg_loss:.4f}, Accuracy: {avg_accuracy:.3f}, "
                f"LR: {self.scheduler.get_last_lr()[0]:.6f}"
            )
            
            # Sauvegarde
            if avg_accuracy > best_accuracy:
                best_accuracy = avg_accuracy
                self.save_model(f"hrm_best.pth")
            
            if (epoch + 1) % self.config.save_every == 0:
                self.save_model(f"hrm_epoch_{epoch+1}.pth")
        
        self.logger.info(f"Entraînement terminé. Meilleure accuracy: {best_accuracy:.3f}")
        return best_accuracy
    
    def save_model(self, filename):
        """Sauvegarde le modèle."""
        checkpoint_dir = Path(self.config.checkpoint_dir)
        checkpoint_dir.mkdir(exist_ok=True)
        
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config,
            'train_losses': self.train_losses,
            'train_accuracies': self.train_accuracies,
            'vocab': GRAMMAR_VOCAB
        }, checkpoint_dir / filename)

def main():
    """Fonction principale d'entraînement."""
    print("🚀 ENTRAÎNEMENT HRM")
    print("=" * 50)
    
    # Configuration
    config = HRMTrainingConfig(
        model_dim=64,  # Réduit pour test
        n_heads=4,
        N_cycles=2,
        T_steps=3,
        batch_size=2,
        num_epochs=20,
        max_puzzles=50,
        learning_rate=1e-4
    )
    
    print(f"Device: {config.device}")
    print(f"Max puzzles: {config.max_puzzles}")
    print(f"Vocabulaire: {VOCAB_SIZE} tokens")
    
    # Dataset
    dataset = ARCHRMDataset(max_puzzles=config.max_puzzles, config=config)
    print(f"Dataset chargé: {len(dataset)} puzzles")
    
    # Entraîneur
    trainer = HRMTrainer(config)
    
    # Entraînement
    best_accuracy = trainer.train(dataset)
    
    print(f"\n✅ Entraînement terminé")
    print(f"Meilleure accuracy: {best_accuracy:.3f}")
    print(f"Modèle sauvé dans: {config.checkpoint_dir}")

if __name__ == "__main__":
    main()
